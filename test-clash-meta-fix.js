/**
 * 测试修复后的 Clash Meta 配置生成
 */

const { NodeConverter } = require('./src/converter.js');
const fs = require('fs');
const yaml = require('js-yaml');

// 测试用的 VLESS-REALITY 链接
const testVlessLink = 'vless://7983e0bb-1ef9-49c5-c41b-e9d1d8040e0f@185.148.13.75:56879?type=tcp&security=reality&sni=www.icloud.com&pbk=g1f1wLjim5gOVGnI5LGUV0dL4iFXPoiepOPZfSxJe14&flow=xtls-rprx-vision&fp=chrome#US-CloudSilk';

console.log('🧪 测试修复后的 Clash Meta 配置生成\n');

const converter = new NodeConverter();

try {
  // 解析节点
  console.log('📋 解析 VLESS-REALITY 节点...');
  const node = converter.parseVless(testVlessLink);
  console.log('✅ 节点解析成功');
  console.log(`   名称: ${node.name}`);
  console.log(`   服务器: ${node.server}:${node.port}`);
  console.log(`   安全: ${node.reality ? 'REALITY' : 'TLS'}`);
  console.log(`   Flow: ${node.flow}`);
  console.log(`   SNI: ${node.sni}`);
  console.log(`   指纹: ${node.fingerprint}`);
  console.log('');

  // 生成带 GeoIP 的配置
  console.log('📋 生成带 GeoIP 的 Clash 配置...');
  const configWithGeo = converter.toClashConfig([node], { geodata: true });
  
  // 保存配置文件
  const yamlWithGeo = yaml.dump(configWithGeo, { 
    indent: 2,
    lineWidth: -1,
    noRefs: true,
    quotingType: '"',
    forceQuotes: false
  });
  
  fs.writeFileSync('clash-meta-with-geo.yaml', yamlWithGeo);
  console.log('✅ 带 GeoIP 配置已保存到 clash-meta-with-geo.yaml');
  
  // 生成不带 GeoIP 的配置
  console.log('📋 生成不带 GeoIP 的 Clash 配置...');
  const configWithoutGeo = converter.toClashConfig([node], { geodata: false });
  
  const yamlWithoutGeo = yaml.dump(configWithoutGeo, { 
    indent: 2,
    lineWidth: -1,
    noRefs: true,
    quotingType: '"',
    forceQuotes: false
  });
  
  fs.writeFileSync('clash-meta-no-geo.yaml', yamlWithoutGeo);
  console.log('✅ 不带 GeoIP 配置已保存到 clash-meta-no-geo.yaml');
  console.log('');

  // 验证配置
  console.log('📋 验证生成的配置...');
  
  function validateConfig(config, name) {
    console.log(`\n🔍 验证 ${name}:`);
    
    // 检查基本字段
    const requiredFields = ['port', 'socks-port', 'mode', 'proxies', 'proxy-groups', 'rules'];
    const missingFields = requiredFields.filter(field => !config[field]);
    
    if (missingFields.length > 0) {
      console.log(`❌ 缺少字段: ${missingFields.join(', ')}`);
      return false;
    }
    
    console.log('✅ 基本字段完整');
    
    // 检查代理配置
    if (config.proxies.length === 0) {
      console.log('❌ 没有代理节点');
      return false;
    }
    
    const proxy = config.proxies[0];
    console.log(`✅ 代理配置: ${proxy.name} (${proxy.type})`);
    
    // 检查 VLESS-REALITY 特定字段
    if (proxy.type === 'vless') {
      const requiredVlessFields = ['server', 'port', 'uuid', 'network', 'flow', 'client-fingerprint'];
      const missingVlessFields = requiredVlessFields.filter(field => !proxy[field]);
      
      if (missingVlessFields.length > 0) {
        console.log(`⚠️  VLESS 配置缺少字段: ${missingVlessFields.join(', ')}`);
      } else {
        console.log('✅ VLESS 配置完整');
      }
      
      // 检查 REALITY 配置
      if (proxy['reality-opts']) {
        console.log('✅ REALITY 配置存在');
        if (proxy['reality-opts']['public-key']) {
          console.log('✅ REALITY 公钥存在');
        } else {
          console.log('⚠️  REALITY 公钥缺失');
        }
      } else {
        console.log('⚠️  REALITY 配置缺失');
      }
    }
    
    // 检查代理组
    console.log(`✅ 代理组数量: ${config['proxy-groups'].length}`);
    
    // 检查规则
    console.log(`✅ 规则数量: ${config.rules.length}`);
    
    // 检查 GeoIP 相关配置
    if (config['geodata-mode']) {
      console.log('✅ GeoIP 模式已启用');
      if (config['geox-url']) {
        console.log('✅ GeoIP 数据源配置存在');
      } else {
        console.log('❌ GeoIP 数据源配置缺失');
      }
    } else {
      console.log('✅ GeoIP 模式已禁用');
    }
    
    return true;
  }
  
  const withGeoValid = validateConfig(configWithGeo, '带 GeoIP 配置');
  const withoutGeoValid = validateConfig(configWithoutGeo, '不带 GeoIP 配置');
  
  console.log('\n📊 配置统计:');
  console.log(`   带 GeoIP 配置: ${withGeoValid ? '✅ 有效' : '❌ 无效'}`);
  console.log(`   不带 GeoIP 配置: ${withoutGeoValid ? '✅ 有效' : '❌ 无效'}`);
  
  console.log('\n💡 使用建议:');
  console.log('   1. 如果遇到 GeoIP 错误，请使用 clash-meta-no-geo.yaml');
  console.log('   2. 如果网络正常，推荐使用 clash-meta-with-geo.yaml');
  console.log('   3. 两个配置都已修复了 YAML 语法问题');

} catch (error) {
  console.error('❌ 测试失败:', error.message);
  console.error(error.stack);
}

console.log('\n✅ 测试完成！');
