const fs = require('fs');
const yaml = require('js-yaml');

function testClashConfig(configPath) {
    console.log(`\n=== 测试配置文件: ${configPath} ===`);
    
    try {
        // 读取配置文件
        const configContent = fs.readFileSync(configPath, 'utf8');
        
        // 解析 YAML
        const config = yaml.load(configContent);
        
        console.log('✅ YAML 格式验证通过');
        
        // 检查必要的字段
        const requiredFields = ['port', 'socks-port', 'mode', 'proxies', 'proxy-groups', 'rules'];
        const missingFields = [];
        
        for (const field of requiredFields) {
            if (!config[field]) {
                missingFields.push(field);
            }
        }
        
        if (missingFields.length > 0) {
            console.log('❌ 缺少必要字段:', missingFields.join(', '));
            return false;
        }
        
        console.log('✅ 必要字段检查通过');
        
        // 检查代理配置
        if (!Array.isArray(config.proxies) || config.proxies.length === 0) {
            console.log('❌ 代理配置为空或格式错误');
            return false;
        }
        
        console.log(`✅ 代理配置检查通过 (${config.proxies.length} 个代理)`);
        
        // 检查代理组配置
        if (!Array.isArray(config['proxy-groups']) || config['proxy-groups'].length === 0) {
            console.log('❌ 代理组配置为空或格式错误');
            return false;
        }
        
        console.log(`✅ 代理组配置检查通过 (${config['proxy-groups'].length} 个代理组)`);
        
        // 检查规则配置
        if (!Array.isArray(config.rules) || config.rules.length === 0) {
            console.log('❌ 规则配置为空或格式错误');
            return false;
        }
        
        console.log(`✅ 规则配置检查通过 (${config.rules.length} 条规则)`);
        
        // 检查 GeoIP 配置
        if (config['geodata-mode']) {
            if (config['geox-url']) {
                console.log('✅ GeoIP 配置检查通过 (启用 geodata 模式)');
            } else {
                console.log('⚠️  启用了 geodata 模式但缺少 geox-url 配置');
            }
        } else {
            console.log('✅ GeoIP 配置检查通过 (禁用 geodata 模式)');
        }
        
        // 检查 DNS 配置
        if (config.dns && config.dns.enable) {
            console.log('✅ DNS 配置检查通过');
        } else {
            console.log('⚠️  DNS 配置未启用');
        }
        
        console.log('🎉 配置文件验证完成，所有检查通过！');
        return true;
        
    } catch (error) {
        console.log('❌ 配置文件验证失败:', error.message);
        
        if (error.name === 'YAMLException') {
            console.log('💡 YAML 语法错误，请检查缩进和格式');
            console.log('错误位置:', error.mark ? `行 ${error.mark.line + 1}, 列 ${error.mark.column + 1}` : '未知');
        }
        
        return false;
    }
}

// 测试两个配置文件
console.log('开始测试 Clash 配置文件...\n');

const configs = [
    'clash-with-relay.yaml',
    'clash-no-geodata.yaml'
];

let allPassed = true;

for (const config of configs) {
    if (fs.existsSync(config)) {
        const result = testClashConfig(config);
        allPassed = allPassed && result;
    } else {
        console.log(`❌ 配置文件不存在: ${config}`);
        allPassed = false;
    }
}

console.log('\n=== 总结 ===');
if (allPassed) {
    console.log('🎉 所有配置文件验证通过！');
} else {
    console.log('❌ 部分配置文件验证失败，请检查错误信息');
}
