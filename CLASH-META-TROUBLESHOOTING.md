# Clash Meta 故障排除指南

## 常见错误及解决方案

### 1. GeoIP 相关错误

#### 错误信息：`[GeoIP] proto: cannot parse invalid wire-format data`

**原因：**
- GeoIP 数据文件下载失败或损坏
- 使用了不兼容的 GeoIP 数据源
- 网络连接问题

**解决方案：**

1. **使用直接下载链接**（推荐）
   ```yaml
   geox-url:
     geoip: https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geoip-lite.dat
     geosite: https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geosite.dat
     mmdb: https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/country-lite.mmdb
   ```

2. **临时禁用 geodata 模式**
   ```yaml
   geodata-mode: false
   ```
   然后在 DNS 配置中禁用 geoip：
   ```yaml
   dns:
     fallback-filter:
       geoip: false
   ```

3. **手动删除缓存文件**
   - 删除 Clash Meta 数据目录中的 `.dat` 和 `.mmdb` 文件
   - 重启 Clash Meta 让其重新下载

### 2. Rules 错误

#### 错误信息：`rules[26]error`

**原因：**
- YAML 语法错误
- 规则格式不正确
- 缩进问题

**解决方案：**

1. **检查 YAML 语法**
   - 确保所有包含特殊字符的字符串都用引号包围
   - 检查缩进是否一致（使用空格，不要使用制表符）

2. **修复常见格式问题**
   ```yaml
   # 错误
   fake-ip-filter:
     - *.lan
     - +.example.com
   
   # 正确
   fake-ip-filter:
     - "*.lan"
     - "+.example.com"
   ```

3. **验证规则格式**
   ```yaml
   rules:
     - DOMAIN-SUFFIX,example.com,PROXY
     - IP-CIDR,***********/16,DIRECT
     - GEOIP,CN,DIRECT
     - MATCH,PROXY
   ```

### 3. 代理配置错误

#### VLESS-REALITY 配置问题

**正确的 VLESS-REALITY 配置：**
```yaml
proxies:
  - name: VLESS-REALITY
    type: vless
    server: example.com
    port: 443
    uuid: your-uuid
    cipher: 
    alterId: 0
    udp: true
    tls: true
    skip-cert-verify: false
    servername: www.icloud.com
    network: tcp
    flow: xtls-rprx-vision
    client-fingerprint: chrome
    reality-opts:
      public-key: your-public-key
```

**注意事项：**
- 使用 `client-fingerprint` 而不是 `fingerprint`
- 包含 `cipher` 和 `alterId` 字段
- 不要包含 `reality: true` 或 `packet-encoding` 字段

### 4. DNS 配置问题

#### DNS 泄露预防配置

```yaml
dns:
  enable: true
  ipv6: false
  enhanced-mode: fake-ip
  fake-ip-range: **********/16
  default-nameserver:
    - ***********
    - https://**********/dns-query
  nameserver:
    - https://doh.pub/dns-query
    - https://dns.alidns.com/dns-query
  fallback:
    - https://*******/dns-query
    - https://*******/dns-query
  fallback-filter:
    geoip: true
    geoip-code: CN
```

### 5. 链式代理配置

#### 正确的链式代理配置

```yaml
proxies:
  - name: 🔗 链式代理
    type: relay
    proxies:
      - 第一跳代理
      - 第二跳代理
      - 第三跳代理
```

### 6. 配置文件验证

使用提供的测试脚本验证配置：
```bash
node test-clash-config.js
```

### 7. 推荐的配置文件

项目中提供了两个配置文件：

1. **clash-with-relay.yaml** - 完整功能版本（包含 GeoIP）
2. **clash-no-geodata.yaml** - 简化版本（不使用 GeoIP）

如果遇到 GeoIP 问题，建议先使用 `clash-no-geodata.yaml` 进行测试。

### 8. 常用调试命令

1. **检查配置文件语法**
   ```bash
   # 使用 YAML 验证工具
   node test-clash-config.js
   ```

2. **查看 Clash Meta 日志**
   - 设置 `log-level: debug` 获取详细日志
   - 检查启动时的错误信息

3. **测试网络连接**
   ```bash
   # 测试代理连接
   curl -x socks5://127.0.0.1:7891 https://www.google.com
   ```

### 9. 性能优化建议

1. **启用并发连接**
   ```yaml
   tcp-concurrent: true
   unified-delay: true
   ```

2. **优化 DNS 设置**
   ```yaml
   dns:
     prefer-h3: true
     use-hosts: true
   ```

3. **启用进程匹配**
   ```yaml
   find-process-mode: strict
   ```

### 10. 获取帮助

如果问题仍然存在：
1. 检查 Clash Meta 官方文档
2. 查看项目的 GitHub Issues
3. 使用提供的测试工具验证配置
4. 检查网络连接和防火墙设置
