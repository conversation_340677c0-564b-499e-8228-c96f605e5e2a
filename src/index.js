import { Auth } from './auth.js';
import { NodeConverter } from './converter.js';

// 模板文件内容 (在实际部署时需要读取文件)
const templates = {
  css: `/* CSS content will be loaded here */`,
  login: `/* Login HTML will be loaded here */`,
  dashboard: `/* Dashboard HTML will be loaded here */`
};

export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const auth = new Auth(env);
    const converter = new NodeConverter();

    // 路由处理
    switch (url.pathname) {
      case '/':
        return this.handleHome(request, auth, env);
      
      case '/login':
        return this.handleLogin(request, auth, env);
      
      case '/logout':
        return this.handleLogout(request, auth);
      
      case '/add-node':
        return this.handleAddNode(request, auth, converter, env);
      
      case '/delete-node':
        return this.handleDeleteNode(request, auth, env);

      case '/edit-node':
        return this.handleEditNode(request, auth, converter, env);

      case '/add-email':
        return this.handleAddEmail(request, auth, env);

      case '/delete-email':
        return this.handleDeleteEmail(request, auth, env);

      case '/v2ray':
        return this.handleV2raySubscription(converter, env, request);

      case '/clash':
        return this.handleClashSubscription(converter, env, request);

      case '/surge':
        return this.handleSurgeSubscription(converter, env, request);

      default:
        return new Response('Not Found', { status: 404 });
    }
  },

  // 处理首页
  async handleHome(request, auth, env) {
    if (!auth.isAuthenticated(request)) {
      return this.renderLogin();
    }
    return this.renderDashboard(request, env);
  },

  // 处理登录
  async handleLogin(request, auth, env) {
    if (request.method === 'GET') {
      return this.renderLogin();
    }

    if (request.method === 'POST') {
      const formData = await request.formData();
      const username = formData.get('username');
      const password = formData.get('password');

      if (auth.validateCredentials(username, password)) {
        const token = auth.generateToken(username);
        const response = new Response('', {
          status: 302,
          headers: {
            'Location': '/',
            'Set-Cookie': auth.createAuthCookie(token)
          }
        });
        return response;
      } else {
        return this.renderLogin('用户名或密码错误');
      }
    }
  },

  // 处理登出
  async handleLogout(request, auth) {
    return new Response('', {
      status: 302,
      headers: {
        'Location': '/',
        'Set-Cookie': auth.createLogoutCookie()
      }
    });
  },

  // 处理添加节点
  async handleAddNode(request, auth, converter, env) {
    if (!auth.isAuthenticated(request)) {
      return new Response('Unauthorized', { status: 401 });
    }

    if (request.method === 'POST') {
      try {
        const formData = await request.formData();
        const type = formData.get('type');
        const name = formData.get('name');
        const config = formData.get('config');

        // 验证输入
        if (!type || !name || !config) {
          throw new Error('请填写完整的节点信息');
        }

        // 解析节点
        const node = converter.parseNode(config.trim(), type);
        node.name = name.trim(); // 使用用户提供的名称
        node.id = Date.now().toString(); // 简单的ID生成

        // 验证解析结果
        if (!node.server || !node.port || !node.uuid) {
          throw new Error('节点配置不完整，请检查链接格式');
        }

        // 获取现有节点
        const existingNodes = await this.getNodes(env);

        // 检查是否已存在相同的节点
        const duplicate = existingNodes.find(existing =>
          existing.server === node.server &&
          existing.port === node.port &&
          existing.uuid === node.uuid
        );

        if (duplicate) {
          throw new Error('该节点已存在');
        }

        existingNodes.push(node);

        // 保存到KV
        await env.NODES_KV.put('nodes', JSON.stringify(existingNodes));

        return new Response('', {
          status: 302,
          headers: { 'Location': '/' }
        });
      } catch (e) {
        console.error('添加节点失败:', e);
        return this.renderDashboard(request, env, `添加节点失败: ${e.message}`);
      }
    }
  },

  // 处理删除节点
  async handleDeleteNode(request, auth, env) {
    if (!auth.isAuthenticated(request)) {
      return new Response('Unauthorized', { status: 401 });
    }

    if (request.method === 'POST') {
      const { id } = await request.json();
      const nodes = await this.getNodes(env);
      const filteredNodes = nodes.filter(node => node.id !== id);
      await env.NODES_KV.put('nodes', JSON.stringify(filteredNodes));
      return new Response('OK');
    }
  },

  // 处理编辑节点
  async handleEditNode(request, auth, converter, env) {
    if (!auth.isAuthenticated(request)) {
      return new Response('Unauthorized', { status: 401 });
    }

    if (request.method === 'GET') {
      // 显示编辑表单
      const url = new URL(request.url);
      const nodeId = url.searchParams.get('id');

      if (!nodeId) {
        return new Response('Missing node ID', { status: 400 });
      }

      const nodes = await this.getNodes(env);
      const node = nodes.find(n => n.id === nodeId);

      if (!node) {
        return new Response('Node not found', { status: 404 });
      }

      return this.renderEditNodeForm(request, env, node);
    }

    if (request.method === 'POST') {
      try {
        const formData = await request.formData();
        const nodeId = formData.get('id');
        const type = formData.get('type');
        const name = formData.get('name');
        const config = formData.get('config');

        // 验证输入
        if (!nodeId || !type || !name || !config) {
          throw new Error('请填写完整的节点信息');
        }

        // 解析节点
        const updatedNode = converter.parseNode(config.trim(), type);
        updatedNode.name = name.trim();
        updatedNode.id = nodeId; // 保持原有ID

        // 验证解析结果
        if (!updatedNode.server || !updatedNode.port || !updatedNode.uuid) {
          throw new Error('节点配置不完整，请检查链接格式');
        }

        // 获取现有节点并更新
        const nodes = await this.getNodes(env);
        const nodeIndex = nodes.findIndex(n => n.id === nodeId);

        if (nodeIndex === -1) {
          throw new Error('节点不存在');
        }

        // 检查是否与其他节点重复
        const duplicate = nodes.find((existing, index) =>
          index !== nodeIndex &&
          existing.server === updatedNode.server &&
          existing.port === updatedNode.port &&
          existing.uuid === updatedNode.uuid
        );

        if (duplicate) {
          throw new Error('该节点配置与其他节点重复');
        }

        nodes[nodeIndex] = updatedNode;

        // 保存到KV
        await env.NODES_KV.put('nodes', JSON.stringify(nodes));

        return new Response('', {
          status: 302,
          headers: { 'Location': '/' }
        });
      } catch (e) {
        console.error('编辑节点失败:', e);
        const url = new URL(request.url);
        const nodeId = url.searchParams.get('id');
        const nodes = await this.getNodes(env);
        const node = nodes.find(n => n.id === nodeId);
        return this.renderEditNodeForm(request, env, node, `编辑节点失败: ${e.message}`);
      }
    }
  },

  // 处理V2Ray订阅
  async handleV2raySubscription(converter, env, request) {
    // 检查邮箱验证
    const url = new URL(request.url);
    const email = url.searchParams.get('email');

    if (!email) {
      return new Response('需要提供email参数', {
        status: 400,
        headers: { 'Content-Type': 'text/plain; charset=utf-8' }
      });
    }

    // 验证邮箱是否在可信列表中
    const isValidEmail = await this.validateEmail(email, env);
    if (!isValidEmail) {
      return new Response('邮箱未授权访问', {
        status: 403,
        headers: { 'Content-Type': 'text/plain; charset=utf-8' }
      });
    }

    const nodes = await this.getNodes(env);
    const subscription = converter.toV2raySubscription(nodes);

    // 检查是否是浏览器访问（通过User-Agent或Accept头判断）
    const userAgent = request.headers.get('User-Agent') || '';
    const accept = request.headers.get('Accept') || '';
    const isBrowser = userAgent.includes('Mozilla') && accept.includes('text/html');

    if (isBrowser) {
      // 浏览器访问时显示纯文本配置内容
      const decodedContent = atob(subscription); // 解码Base64
      return new Response(decodedContent, {
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
          'Cache-Control': 'no-cache'
        }
      });
    } else {
      // 客户端访问时返回订阅内容
      return new Response(subscription, {
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
          'Cache-Control': 'no-cache'
        }
      });
    }
  },

  // 处理Clash订阅
  async handleClashSubscription(converter, env, request) {
    // 检查邮箱验证
    const url = new URL(request.url);
    const email = url.searchParams.get('email');

    if (!email) {
      return new Response('需要提供email参数', {
        status: 400,
        headers: { 'Content-Type': 'text/plain; charset=utf-8' }
      });
    }

    // 验证邮箱是否在可信列表中
    const isValidEmail = await this.validateEmail(email, env);
    if (!isValidEmail) {
      return new Response('邮箱未授权访问', {
        status: 403,
        headers: { 'Content-Type': 'text/plain; charset=utf-8' }
      });
    }

    const nodes = await this.getNodes(env);
    const clashConfig = converter.toClashConfig(nodes);

    // 将JSON转换为YAML格式
    const yamlContent = this.jsonToYaml(clashConfig);

    // 直接返回YAML内容，无论是浏览器还是客户端访问
    return new Response(yamlContent, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache'
      }
    });
  },

  // 处理Surge订阅
  async handleSurgeSubscription(converter, env, request) {
    // 检查邮箱验证
    const url = new URL(request.url);
    const email = url.searchParams.get('email');

    if (!email) {
      return new Response('需要提供email参数', {
        status: 400,
        headers: { 'Content-Type': 'text/plain; charset=utf-8' }
      });
    }

    // 验证邮箱是否在可信列表中
    const isValidEmail = await this.validateEmail(email, env);
    if (!isValidEmail) {
      return new Response('邮箱未授权访问', {
        status: 403,
        headers: { 'Content-Type': 'text/plain; charset=utf-8' }
      });
    }

    const nodes = await this.getNodes(env);
    const surgeConfig = this.generateSurgeConfig(nodes);

    // 直接返回配置内容，无论是浏览器还是客户端访问
    return new Response(surgeConfig, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache'
      }
    });
  },

  // 获取节点列表
  async getNodes(env) {
    try {
      const nodesData = await env.NODES_KV.get('nodes');
      return nodesData ? JSON.parse(nodesData) : [];
    } catch (e) {
      return [];
    }
  },

  // 获取可信邮箱列表
  async getTrustedEmails(env) {
    try {
      const emailsData = await env.NODES_KV.get('trusted_emails');
      return emailsData ? JSON.parse(emailsData) : [];
    } catch (e) {
      return [];
    }
  },

  // 验证邮箱是否可信
  async validateEmail(email, env) {
    const trustedEmails = await this.getTrustedEmails(env);
    return trustedEmails.includes(email.toLowerCase());
  },

  // 处理添加邮箱
  async handleAddEmail(request, auth, env) {
    if (!auth.isAuthenticated(request)) {
      return new Response('Unauthorized', { status: 401 });
    }

    if (request.method === 'POST') {
      try {
        const formData = await request.formData();
        const email = formData.get('email');

        // 验证邮箱格式
        if (!email || !this.isValidEmailFormat(email)) {
          throw new Error('请输入有效的邮箱地址');
        }

        // 获取现有邮箱列表
        const existingEmails = await this.getTrustedEmails(env);
        const normalizedEmail = email.toLowerCase().trim();

        // 检查是否已存在
        if (existingEmails.includes(normalizedEmail)) {
          throw new Error('该邮箱已存在');
        }

        existingEmails.push(normalizedEmail);

        // 保存到KV
        await env.NODES_KV.put('trusted_emails', JSON.stringify(existingEmails));

        return new Response('', {
          status: 302,
          headers: { 'Location': '/' }
        });
      } catch (e) {
        console.error('添加邮箱失败:', e);
        return this.renderDashboard(request, env, `添加邮箱失败: ${e.message}`);
      }
    }
  },

  // 处理删除邮箱
  async handleDeleteEmail(request, auth, env) {
    if (!auth.isAuthenticated(request)) {
      return new Response('Unauthorized', { status: 401 });
    }

    if (request.method === 'POST') {
      const { email } = await request.json();
      const emails = await this.getTrustedEmails(env);
      const filteredEmails = emails.filter(e => e !== email);
      await env.NODES_KV.put('trusted_emails', JSON.stringify(filteredEmails));
      return new Response('OK');
    }
  },

  // 验证邮箱格式
  isValidEmailFormat(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // 渲染登录页面
  renderLogin(errorMessage = '') {
    const css = this.getCSS();
    let html = this.getLoginHTML();
    html = html.replace('{{CSS}}', css);
    html = html.replace('{{ERROR_MESSAGE}}', errorMessage ? 
      `<div class="alert alert-error">${errorMessage}</div>` : '');
    
    return new Response(html, {
      headers: { 'Content-Type': 'text/html; charset=utf-8' }
    });
  },

  // 渲染管理面板
  async renderDashboard(request, env, errorMessage = '') {
    const css = this.getCSS();
    const nodes = await this.getNodes(env);
    const emails = await this.getTrustedEmails(env);
    const baseUrl = new URL(request.url).origin;

    let html = this.getDashboardHTML();
    html = html.replace('{{CSS}}', css);
    // 替换所有的 {{BASE_URL}} 占位符
    html = html.replace(/\{\{BASE_URL\}\}/g, baseUrl);
    html = html.replace('{{NODES_LIST}}', this.generateNodesList(nodes));
    html = html.replace('{{EMAILS_LIST}}', this.generateEmailsList(emails));
    html = html.replace('{{ERROR_MESSAGE}}', errorMessage ?
      `<div class="alert alert-error">${errorMessage}</div>` : '');

    return new Response(html, {
      headers: { 'Content-Type': 'text/html; charset=utf-8' }
    });
  },

  // 生成节点列表HTML
  generateNodesList(nodes) {
    if (nodes.length === 0) {
      return '<div class="empty-state">暂无节点，请添加节点</div>';
    }

    return nodes.map(node => `
      <div class="node-item">
        <div class="node-info">
          <div class="node-name">${node.name}</div>
          <div class="node-details">
            <span class="node-type">${node.type.toUpperCase()}</span>
            <span class="node-server">${node.server}:${node.port}</span>
          </div>
        </div>
        <div class="node-actions">
          <button class="btn-edit" onclick="editNode('${node.id}')">✏️ 编辑</button>
          <button class="btn-danger" onclick="deleteNode('${node.id}')">🗑️ 删除</button>
        </div>
      </div>
    `).join('');
  },

  // 生成邮箱列表HTML
  generateEmailsList(emails) {
    if (emails.length === 0) {
      return '<div class="empty-state">暂无可信邮箱，请添加邮箱</div>';
    }

    return emails.map(email => `
      <div class="email-item">
        <div class="email-info">
          <div class="email-address">📧 ${email}</div>
        </div>
        <div class="email-actions">
          <button class="btn-danger" onclick="deleteEmail('${email}')">🗑️ 删除</button>
        </div>
      </div>
    `).join('');
  },

  // 渲染编辑节点表单
  async renderEditNodeForm(request, env, node, errorMessage = '') {
    const css = this.getCSS();
    const baseUrl = new URL(request.url).origin;

    // 重新生成节点配置链接
    let nodeConfig = '';
    const { NodeConverter } = require('./converter.js');
    const converter = new NodeConverter();

    if (node.type === 'vmess') {
      nodeConfig = converter.nodeToVmessLink(node);
    } else if (node.type === 'vless') {
      nodeConfig = converter.nodeToVlessLink(node);
    }

    const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑节点 - 订阅转换器</title>
    <style>${css}</style>
</head>
<body>
    <div class="dashboard-container">
        <div class="container">
            <div class="header">
                <h1>✏️ 编辑节点</h1>
                <p>修改节点配置信息</p>
            </div>

            ${errorMessage ? `<div class="alert alert-error">${errorMessage}</div>` : ''}

            <div class="edit-page-grid">
                <div class="container">
                    <h2>📝 节点信息</h2>
                    <form method="POST" action="/edit-node">
                        <input type="hidden" name="id" value="${node.id}">
                        <div class="form-group">
                            <label for="node-type">🔧 节点类型:</label>
                            <select id="node-type" name="type" required>
                                <option value="vmess" ${node.type === 'vmess' ? 'selected' : ''}>VMess</option>
                                <option value="vless" ${node.type === 'vless' ? 'selected' : ''}>VLess</option>
                                <option value="ss" ${node.type === 'ss' ? 'selected' : ''}>Shadowsocks</option>
                                <option value="trojan" ${node.type === 'trojan' ? 'selected' : ''}>Trojan</option>
                                <option value="http" ${node.type === 'http' ? 'selected' : ''}>HTTP/HTTPS</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="node-name">📝 节点名称:</label>
                            <input type="text" id="node-name" name="name" value="${node.name}" required placeholder="例如: 香港节点01">
                        </div>
                        <div class="form-group">
                            <label for="node-config">⚙️ 节点配置:</label>
                            <textarea id="node-config" name="config" rows="6" required placeholder="粘贴节点链接或配置信息">${nodeConfig}</textarea>
                            <small class="form-help">支持 VLess、VMess、Shadowsocks、Trojan 等格式的链接</small>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn-primary">💾 保存修改</button>
                            <a href="/" class="btn-secondary">❌ 取消</a>
                        </div>
                    </form>
                </div>

                <!-- 当前配置预览 -->
                <div class="container">
                    <h2>🔍 当前配置</h2>
                    <div class="config-preview">
                        <div class="config-item">
                            <strong>服务器:</strong> ${node.server}:${node.port}
                        </div>
                        <div class="config-item">
                            <strong>UUID:</strong> ${node.uuid}
                        </div>
                        ${node.network ? `<div class="config-item"><strong>网络:</strong> ${node.network}</div>` : ''}
                        ${node.security ? `<div class="config-item"><strong>安全:</strong> ${node.security}</div>` : ''}
                        ${node.sni ? `<div class="config-item"><strong>SNI:</strong> ${node.sni}</div>` : ''}
                        ${node.flow ? `<div class="config-item"><strong>Flow:</strong> ${node.flow}</div>` : ''}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = \`notification notification-\${type}\`;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => notification.classList.add('show'), 100);
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 3000);
        }
    </script>
</body>
</html>`;

    return new Response(html, {
      headers: { 'Content-Type': 'text/html; charset=utf-8' }
    });
  },

  // 生成Surge配置
  generateSurgeConfig(nodes) {
    const proxies = nodes.map(node => {
      switch (node.type) {
        case 'ss':
          return `${node.name} = ss, ${node.server}, ${node.port}, encrypt-method=${node.method}, password=${node.password}`;
        case 'http':
          return `${node.name} = http, ${node.server}, ${node.port}${node.username ? `, username=${node.username}, password=${node.password}` : ''}`;
        default:
          return null;
      }
    }).filter(Boolean);

    return `[General]
skip-proxy = 127.0.0.1, ***********/16, 10.0.0.0/8, **********/12, localhost, *.local

[Proxy]
${proxies.join('\n')}

[Proxy Group]
Proxy = select, ${nodes.map(n => n.name).join(', ')}

[Rule]
FINAL,Proxy`;
  },

  // 简单的JSON到YAML转换器
  jsonToYaml(obj, indent = 0) {
    const spaces = '  '.repeat(indent);
    let yaml = '';

    if (Array.isArray(obj)) {
      for (const item of obj) {
        if (typeof item === 'object' && item !== null) {
          yaml += `${spaces}- ${this.jsonToYaml(item, indent + 1).trim()}\n`;
        } else {
          yaml += `${spaces}- ${this.escapeYamlValue(item)}\n`;
        }
      }
    } else if (typeof obj === 'object' && obj !== null) {
      for (const [key, value] of Object.entries(obj)) {
        if (Array.isArray(value)) {
          yaml += `${spaces}${key}:\n`;
          yaml += this.jsonToYaml(value, indent + 1);
        } else if (typeof value === 'object' && value !== null) {
          yaml += `${spaces}${key}:\n`;
          yaml += this.jsonToYaml(value, indent + 1);
        } else {
          yaml += `${spaces}${key}: ${this.escapeYamlValue(value)}\n`;
        }
      }
    }

    return yaml;
  },

  // 转义YAML值
  escapeYamlValue(value) {
    if (typeof value === 'string') {
      // 如果字符串包含特殊字符，需要加引号
      if (value.includes(':') || value.includes('#') || value.includes('[') ||
          value.includes(']') || value.includes('{') || value.includes('}') ||
          value.includes('|') || value.includes('>') || value.includes('&') ||
          value.includes('*') || value.includes('!') || value.includes('%') ||
          value.includes('@') || value.includes('`')) {
        return `"${value.replace(/"/g, '\\"')}"`;
      }
      return value;
    }
    return value;
  },

  // 生成配置显示页面
  generateConfigDisplayPage(type, content, nodes, rawContent) {
    const nodeCount = nodes.length;

    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${type} 配置 - 订阅转换器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 1rem;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            padding: 2rem;
            background: #f8f9fa;
        }
        .stat-item {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        .content-section {
            padding: 2rem;
        }
        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        .content-title {
            font-size: 1.5rem;
            color: #333;
        }
        .copy-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        .copy-btn:hover {
            background: #218838;
            transform: translateY(-2px);
        }
        .config-content {
            background: #1e1e1e;
            color: #d4d4d4;
            border-radius: 10px;
            padding: 1.5rem;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.6;
            overflow-x: auto;
            white-space: pre-wrap;
            word-break: break-all;
            max-height: 600px;
            overflow-y: auto;
            border: 1px solid #333;
        }
        .usage-info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 1.5rem;
            margin: 2rem 0;
            border-radius: 0 8px 8px 0;
        }
        .usage-info h3 {
            color: #1976d2;
            margin-bottom: 1rem;
        }
        .usage-info ul {
            list-style: none;
            padding-left: 0;
        }
        .usage-info li {
            margin-bottom: 0.5rem;
            padding-left: 1.5rem;
            position: relative;
        }
        .usage-info li:before {
            content: "💡";
            position: absolute;
            left: 0;
        }
        .footer {
            text-align: center;
            padding: 2rem;
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }
        .footer a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        .footer a:hover {
            text-decoration: underline;
        }
        .success-message {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 1000;
        }
        .success-message.show {
            transform: translateX(0);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 ${type} 配置文件</h1>
            <p>订阅转换器生成的配置内容</p>
        </div>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-number">${nodeCount}</div>
                <div class="stat-label">节点数量</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">${Math.round(content.length / 1024)}KB</div>
                <div class="stat-label">配置大小</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">${content.split('\\n').length}</div>
                <div class="stat-label">配置行数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">${type}</div>
                <div class="stat-label">配置类型</div>
            </div>
        </div>

        <div class="usage-info">
            <h3>📖 使用说明</h3>
            <ul>
                <li><strong>客户端导入</strong>: 复制当前页面URL到${type}客户端的订阅链接中</li>
                <li><strong>手动配置</strong>: 点击"复制配置"按钮，将内容粘贴到客户端配置文件中</li>
                <li><strong>自动更新</strong>: 客户端会定期从此链接更新配置</li>
                <li><strong>浏览器访问</strong>: 显示此页面；客户端访问: 直接返回配置内容</li>
            </ul>
        </div>

        <div class="content-section">
            <div class="content-header">
                <h2 class="content-title">📄 配置内容</h2>
                <button class="copy-btn" onclick="copyConfig()">📋 复制配置</button>
            </div>
            <div class="config-content" id="config-content">${content}</div>
        </div>

        <div class="footer">
            <a href="/">← 返回管理面板</a>
        </div>
    </div>

    <div class="success-message" id="success-message">
        ✅ 配置已复制到剪贴板！
    </div>

    <script>
        function copyConfig() {
            const content = \`${rawContent.replace(/`/g, '\\`').replace(/\$/g, '\\$').replace(/\\\\/g, '\\\\\\\\')}\`;
            navigator.clipboard.writeText(content).then(function() {
                showSuccessMessage();
                const btn = document.querySelector('.copy-btn');
                const originalText = btn.textContent;
                btn.textContent = '✅ 已复制!';
                btn.style.background = '#28a745';
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.style.background = '#28a745';
                }, 2000);
            }).catch(function(err) {
                console.error('复制失败:', err);
                alert('复制失败，请手动选择内容复制');
            });
        }

        function showSuccessMessage() {
            const message = document.getElementById('success-message');
            message.classList.add('show');
            setTimeout(() => {
                message.classList.remove('show');
            }, 3000);
        }

        // 语法高亮（简单版）
        function highlightSyntax() {
            const content = document.getElementById('config-content');
            let html = content.textContent;

            // 简单的YAML语法高亮
            if ('${type}' === 'Clash') {
                html = html.replace(/^([a-zA-Z-]+):/gm, '<span style="color: #569cd6;">$1</span>:');
                html = html.replace(/: ([^\\n]+)/g, ': <span style="color: #ce9178;">$1</span>');
                html = html.replace(/#[^\\n]*/g, '<span style="color: #6a9955;">$&</span>');
            }

            content.innerHTML = html;
        }

        // 页面加载完成后执行语法高亮
        document.addEventListener('DOMContentLoaded', highlightSyntax);
    </script>
</body>
</html>`;
  },

  // 获取CSS内容
  getCSS() {
    return `* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* 登录页面样式 */
.login-body {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 1rem;
}

/* 移动端优先的body布局 */
@media (max-width: 768px) {
  body {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* PC端的body布局 */
@media (min-width: 769px) {
  body {
    padding: 2rem 0;
  }
}

.container {
  background: white;
  border-radius: 10px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  width: 100%;
  margin-bottom: 2rem;
}

/* 移动端容器样式 */
@media (max-width: 768px) {
  .container {
    max-width: 400px;
    margin-bottom: 1rem;
  }
}

/* 平板端容器样式 */
@media (min-width: 769px) and (max-width: 1024px) {
  .container {
    max-width: 600px;
    margin: 0 auto 2rem auto;
  }
}

/* PC端容器样式 */
@media (min-width: 1025px) {
  .container {
    max-width: 800px;
    margin: 0 auto 2rem auto;
  }
}

.dashboard-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* PC端dashboard容器优化 */
@media (min-width: 1025px) {
  .dashboard-container {
    padding: 0 2rem;
  }
}

/* 响应式网格布局 */
.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-top: 2rem;
}

/* 平板端：2列布局 */
@media (min-width: 768px) {
  .dashboard-grid {
    grid-template-columns: 1fr 1fr;
  }
}

/* PC端：保持2列布局，更好的比例 */
@media (min-width: 1024px) {
  .dashboard-grid {
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    max-width: 1200px;
    margin: 2rem auto 0 auto;
  }
}

/* 编辑页面的特殊布局 */
.edit-page-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-top: 2rem;
}

@media (min-width: 768px) {
  .edit-page-grid {
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }
}

@media (min-width: 1024px) {
  .edit-page-grid {
    max-width: 1000px;
    margin: 2rem auto 0 auto;
  }
}

h1, h2 {
  text-align: center;
  color: #333;
  margin-bottom: 1.5rem;
}

/* PC端标题优化 */
@media (min-width: 1025px) {
  h1 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
  }

  h2 {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
  }
}

.form-group {
  margin-bottom: 1rem;
}

/* PC端表单组优化 */
@media (min-width: 1025px) {
  .form-group {
    margin-bottom: 1.5rem;
  }
}

label {
  display: block;
  margin-bottom: 0.5rem;
  color: #555;
  font-weight: 500;
}

input, textarea, select {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e1e5e9;
  border-radius: 5px;
  font-size: 1rem;
  transition: border-color 0.3s;
}

/* PC端输入框优化 */
@media (min-width: 1025px) {
  input, textarea, select {
    padding: 1rem;
    font-size: 1.1rem;
  }
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

button {
  width: 100%;
  padding: 0.75rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

/* PC端按钮优化 */
@media (min-width: 1025px) {
  button {
    padding: 1rem;
    font-size: 1.1rem;
    font-weight: 500;
  }
}

button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
  background: #6c757d;
  margin-top: 0.5rem;
}

.btn-danger {
  background: #dc3545;
  width: auto;
  padding: 0.5rem 1rem;
  margin-left: 0.5rem;
}

.btn-edit {
  background: #28a745;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 0.5rem 1rem;
  margin-right: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  width: auto;
}

.btn-edit:hover {
  background: #218838;
  transform: translateY(-1px);
}

.node-item {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
}

/* PC端节点项目优化 */
@media (min-width: 1025px) {
  .node-item {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border-radius: 10px;
  }

  .node-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }
}

.node-item:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  transform: translateY(-2px);
}

.node-info {
  flex: 1;
}

.node-name {
  font-weight: bold;
  font-size: 1.1rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.node-details {
  display: flex;
  gap: 1rem;
  align-items: center;
  font-size: 0.9rem;
  color: #666;
  line-height: 1.5;
}

/* PC端节点详情优化 */
@media (min-width: 1025px) {
  .node-details {
    font-size: 1rem;
  }
}

.node-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.node-type {
  background: #007bff;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* PC端节点类型标签优化 */
@media (min-width: 1025px) {
  .node-type {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
    border-radius: 6px;
  }
}

.subscription-links {
  background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
  border-radius: 8px;
  padding: 1rem;
  margin-top: 2rem;
  border: 1px solid #dee2e6;
}

/* PC端订阅链接区域优化 */
@media (min-width: 1025px) {
  .subscription-links {
    padding: 2rem;
    border-radius: 12px;
    margin-top: 3rem;
  }
}

.link-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  padding: 0.75rem;
  background: white;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  flex-wrap: wrap;
  gap: 0.5rem;
}

/* PC端链接项目优化 */
@media (min-width: 1025px) {
  .link-item {
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    border-radius: 8px;
    font-size: 1.1rem;
  }

  .link-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.link-item span {
  font-weight: 500;
  color: #333;
  min-width: 100px;
}

.copy-btn {
  background: #28a745;
  width: auto;
  padding: 0.4rem 0.8rem;
  font-size: 0.85rem;
  font-weight: 500;
  border-radius: 4px;
  transition: all 0.3s ease;
  min-width: 80px;
  flex-shrink: 0;
}

/* PC端复制按钮优化 */
@media (min-width: 1025px) {
  .copy-btn {
    padding: 0.6rem 1.2rem;
    font-size: 0.95rem;
    border-radius: 6px;
  }
}

.copy-btn:hover {
  background: #218838;
  transform: translateY(-1px);
}

/* 移动端优化 */
@media (max-width: 768px) {
  .container {
    padding: 1.5rem;
    margin: 0.5rem;
    max-width: none;
  }

  .dashboard-container {
    padding: 0.5rem;
  }

  .link-item {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }

  .link-item span {
    margin-bottom: 0.5rem;
    min-width: auto;
  }

  .copy-btn {
    width: 100%;
    min-width: auto;
  }

  .node-header {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .btn-danger {
    margin-left: 0;
    width: 100%;
  }
}

.alert {
  padding: 0.75rem;
  margin-bottom: 1rem;
  border-radius: 6px;
  font-weight: 500;
  border-left: 4px solid;
}

/* PC端警告框优化 */
@media (min-width: 1025px) {
  .alert {
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    border-radius: 8px;
    font-size: 1.05rem;
  }
}

.alert-success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
  border-left-color: #28a745;
}

.alert-error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-left-color: #dc3545;
}

/* 添加一些额外的PC端优化 */
@media (min-width: 1025px) {
  /* 优化删除按钮 */
  .btn-danger {
    padding: 0.6rem 1.2rem;
    font-size: 0.95rem;
    border-radius: 6px;
    transition: all 0.3s ease;
  }

  .btn-danger:hover {
    background: #c82333;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
  }

  /* 优化次要按钮 */
  .btn-secondary {
    margin-top: 1rem;
  }

  /* 优化文本区域 */
  textarea {
    min-height: 120px;
    resize: vertical;
  }

  /* 添加平滑滚动 */
  html {
    scroll-behavior: smooth;
  }

  /* 优化选择框 */
  select {
    cursor: pointer;
  }

  /* 添加加载状态样式 */
  button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
  }
}

/* 主页面特殊布局优化 */
.main-content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
}

/* 节点列表容器优化 */
.nodes-container {
  margin-top: 2rem;
}

@media (min-width: 1024px) {
  .nodes-container {
    margin-top: 3rem;
  }

  /* PC端节点列表全宽显示 */
  .nodes-container .container {
    max-width: 100%;
  }
}

/* 退出登录按钮容器 */
.logout-container {
  margin-top: 2rem;
  text-align: center;
}

@media (min-width: 1024px) {
  .logout-container {
    margin-top: 3rem;
  }
}

.empty-state {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #dee2e6;
  margin: 1rem 0;
}

.config-preview {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1rem;
}

.config-item {
  margin-bottom: 0.75rem;
  padding: 0.5rem;
  background: white;
  border-radius: 4px;
  border-left: 3px solid #007bff;
}

.config-item:last-child {
  margin-bottom: 0;
}

.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.form-actions .btn-secondary {
  background: #6c757d;
  color: white;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  margin-top: 0;
  flex: 1;
}

.form-help {
  color: #666;
  font-size: 0.85rem;
  margin-top: 0.25rem;
  display: block;
}

.header {
  text-align: center;
  margin-bottom: 2rem;
}

.header h1 {
  color: #333;
  margin-bottom: 0.5rem;
}

.header p {
  color: #666;
  margin: 0;
}

.node-server {
  color: #666;
  font-size: 0.9rem;
}`;
  },

  // 获取登录页面HTML
  getLoginHTML() {
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订阅转换器 - 登录</title>
    <style>{{CSS}}</style>
</head>
<body class="login-body">
    <div class="container">
        <h1>🚀 订阅转换器</h1>
        <p style="text-align: center; color: #666; margin-bottom: 2rem;">安全的节点订阅管理平台</p>
        {{ERROR_MESSAGE}}
        <form method="POST" action="/login">
            <div class="form-group">
                <label for="username">👤 用户名:</label>
                <input type="text" id="username" name="username" required autocomplete="username">
            </div>
            <div class="form-group">
                <label for="password">🔒 密码:</label>
                <input type="password" id="password" name="password" required autocomplete="current-password">
            </div>
            <button type="submit">登录</button>
        </form>
    </div>
</body>
</html>`;
  },

  // 获取管理面板HTML
  getDashboardHTML() {
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订阅转换器 - 管理面板</title>
    <style>{{CSS}}</style>
</head>
<body>
    <div class="dashboard-container">
        <header style="text-align: center; margin-bottom: 2rem;">
            <h1>🚀 节点管理面板</h1>
            <p style="color: #666;">管理你的代理节点和订阅链接</p>
        </header>

        {{ERROR_MESSAGE}}

        <div class="main-content-wrapper">
            <div class="dashboard-grid">
                <!-- 添加节点表单 -->
                <div class="container">
                    <h2>➕ 添加新节点</h2>
                    <form method="POST" action="/add-node">
                        <div class="form-group">
                            <label for="node-type">🔧 节点类型:</label>
                            <select id="node-type" name="type" required>
                                <option value="">选择节点类型</option>
                                <option value="vmess">VMess</option>
                                <option value="vless">VLess</option>
                                <option value="ss">Shadowsocks</option>
                                <option value="trojan">Trojan</option>
                                <option value="http">HTTP/HTTPS</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="node-name">📝 节点名称:</label>
                            <input type="text" id="node-name" name="name" required placeholder="例如: 香港节点01">
                        </div>
                        <div class="form-group">
                            <label for="node-config">⚙️ 节点配置:</label>
                        <textarea id="node-config" name="config" rows="4" required placeholder="粘贴节点链接或配置信息"></textarea>
                    </div>
                    <button type="submit">添加节点</button>
                </form>
            </div>

            <!-- 订阅链接 -->
            <div class="container subscription-links">
                <h2>📋 订阅链接</h2>
                <div class="link-item">
                    <span>📱 V2Ray订阅:</span>
                    <button class="copy-btn" onclick="copySubscriptionLink('v2ray')">复制链接</button>
                </div>
                <div class="link-item">
                    <span>⚔️ Clash订阅:</span>
                    <button class="copy-btn" onclick="copySubscriptionLink('clash')">复制链接</button>
                </div>
                <div class="link-item">
                    <span>🌊 Surge订阅:</span>
                    <button class="copy-btn" onclick="copySubscriptionLink('surge')">复制链接</button>
                </div>
                <div style="margin-top: 1rem; padding: 0.75rem; background: #e3f2fd; border-radius: 5px; font-size: 0.9rem; color: #1565c0;">
                    💡 提示: 所有订阅都需要邮箱验证，格式为 /订阅类型?email=<EMAIL>
                </div>
            </div>

            <!-- 邮箱管理 -->
            <div class="container">
                <h2>📧 可信邮箱管理</h2>
                <form method="POST" action="/add-email">
                    <div class="form-group">
                        <label for="email">📧 邮箱地址:</label>
                        <input type="email" id="email" name="email" required placeholder="例如: <EMAIL>">
                    </div>
                    <button type="submit">添加邮箱</button>
                </form>
                <div style="margin-top: 1rem; padding: 0.75rem; background: #fff3e0; border-radius: 5px; font-size: 0.9rem; color: #e65100;">
                    ⚠️ 只有添加到可信列表的邮箱才能访问所有订阅（V2Ray、Clash、Surge）
                </div>
            </div>

            <!-- 邮箱列表 -->
            <div class="container">
                <h2>📋 已添加的邮箱</h2>
                {{EMAILS_LIST}}
            </div>
        </div>

            <!-- 节点列表 -->
            <div class="nodes-container">
                <div class="container">
                    <h2>📊 已添加的节点</h2>
                    {{NODES_LIST}}
                </div>
            </div>

            <!-- 退出登录 -->
            <div class="logout-container">
                <div class="container">
                    <form method="POST" action="/logout">
                        <button type="submit" class="btn-secondary">🚪 退出登录</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                showNotification('✅ 链接已复制到剪贴板', 'success');
            }).catch(function(err) {
                console.error('复制失败:', err);
                showNotification('❌ 复制失败，请手动复制', 'error');
                // 降级方案：显示链接让用户手动复制
                prompt('请手动复制以下链接:', text);
            });
        }

        function copySubscriptionLink(type) {
            const email = prompt('请输入你的邮箱地址:', '');
            if (!email) {
                showNotification('❌ 需要提供邮箱地址', 'error');
                return;
            }

            // 简单的邮箱格式验证
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showNotification('❌ 邮箱格式不正确', 'error');
                return;
            }

            const baseUrl = window.location.origin;
            const subscriptionUrl = \`\${baseUrl}/\${type}?email=\${encodeURIComponent(email)}\`;

            copyToClipboard(subscriptionUrl);
        }

        function deleteNode(nodeId) {
            if (confirm('确定要删除这个节点吗？')) {
                const button = event.target;
                button.disabled = true;
                button.textContent = '删除中...';

                fetch('/delete-node', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: nodeId })
                }).then(response => {
                    if (response.ok) {
                        showNotification('✅ 节点删除成功', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        throw new Error('删除失败');
                    }
                }).catch(error => {
                    console.error('删除失败:', error);
                    showNotification('❌ 删除失败，请重试', 'error');
                    button.disabled = false;
                    button.textContent = '删除';
                });
            }
        }

        function editNode(nodeId) {
            window.location.href = '/edit-node?id=' + nodeId;
        }

        function deleteEmail(email) {
            if (confirm('确定要删除这个邮箱吗？')) {
                const button = event.target;
                button.disabled = true;
                button.textContent = '删除中...';

                fetch('/delete-email', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email: email })
                }).then(response => {
                    if (response.ok) {
                        showNotification('✅ 邮箱删除成功', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        throw new Error('删除失败');
                    }
                }).catch(error => {
                    console.error('删除失败:', error);
                    showNotification('❌ 删除失败，请重试', 'error');
                    button.disabled = false;
                    button.textContent = '🗑️ 删除';
                });
            }
        }

        function showNotification(message, type) {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = \`alert alert-\${type === 'success' ? 'success' : 'error'}\`;
            notification.style.cssText = \`
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
                min-width: 300px;
                animation: slideIn 0.3s ease-out;
            \`;
            notification.textContent = message;

            // 添加动画样式
            if (!document.querySelector('#notification-styles')) {
                const style = document.createElement('style');
                style.id = 'notification-styles';
                style.textContent = \`
                    @keyframes slideIn {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                    @keyframes slideOut {
                        from { transform: translateX(0); opacity: 1; }
                        to { transform: translateX(100%); opacity: 0; }
                    }
                \`;
                document.head.appendChild(style);
            }

            document.body.appendChild(notification);

            // 3秒后自动移除
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 表单提交增强
        document.addEventListener('DOMContentLoaded', function() {
            const nodeForm = document.querySelector('form[action="/add-node"]');
            if (nodeForm) {
                nodeForm.addEventListener('submit', function(e) {
                    const button = nodeForm.querySelector('button[type="submit"]');
                    button.disabled = true;
                    button.textContent = '添加中...';

                    // 如果表单提交失败，恢复按钮状态
                    setTimeout(() => {
                        button.disabled = false;
                        button.textContent = '添加节点';
                    }, 5000);
                });
            }

            const emailForm = document.querySelector('form[action="/add-email"]');
            if (emailForm) {
                emailForm.addEventListener('submit', function(e) {
                    const button = emailForm.querySelector('button[type="submit"]');
                    button.disabled = true;
                    button.textContent = '添加中...';

                    // 如果表单提交失败，恢复按钮状态
                    setTimeout(() => {
                        button.disabled = false;
                        button.textContent = '添加邮箱';
                    }, 5000);
                });
            }
        });
    </script>
</body>
</html>`;
  }
};
