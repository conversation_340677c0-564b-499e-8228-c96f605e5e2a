* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* 移动端优先的body布局 */
@media (max-width: 768px) {
  body {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* PC端的body布局 */
@media (min-width: 769px) {
  body {
    padding: 2rem 0;
  }
}

.container {
  background: white;
  border-radius: 10px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  width: 100%;
  margin-bottom: 2rem;
}

/* 移动端容器样式 */
@media (max-width: 768px) {
  .container {
    max-width: 400px;
    margin-bottom: 1rem;
  }
}

/* 平板端容器样式 */
@media (min-width: 769px) and (max-width: 1024px) {
  .container {
    max-width: 600px;
    margin: 0 auto 2rem auto;
  }
}

/* PC端容器样式 */
@media (min-width: 1025px) {
  .container {
    max-width: 800px;
    margin: 0 auto 2rem auto;
  }
}

.dashboard-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* PC端dashboard容器优化 */
@media (min-width: 1025px) {
  .dashboard-container {
    padding: 0 2rem;
  }
}

h1, h2 {
  text-align: center;
  color: #333;
  margin-bottom: 1.5rem;
}

/* PC端标题优化 */
@media (min-width: 1025px) {
  h1 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
  }

  h2 {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
  }
}

.form-group {
  margin-bottom: 1rem;
}

/* PC端表单组优化 */
@media (min-width: 1025px) {
  .form-group {
    margin-bottom: 1.5rem;
  }
}

label {
  display: block;
  margin-bottom: 0.5rem;
  color: #555;
  font-weight: 500;
}

input, textarea, select {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e1e5e9;
  border-radius: 5px;
  font-size: 1rem;
  transition: border-color 0.3s;
}

/* PC端输入框优化 */
@media (min-width: 1025px) {
  input, textarea, select {
    padding: 1rem;
    font-size: 1.1rem;
  }
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

button {
  width: 100%;
  padding: 0.75rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

/* PC端按钮优化 */
@media (min-width: 1025px) {
  button {
    padding: 1rem;
    font-size: 1.1rem;
    font-weight: 500;
  }
}

button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
  background: #6c757d;
  margin-top: 0.5rem;
}

.btn-danger {
  background: #dc3545;
  width: auto;
  padding: 0.5rem 1rem;
  margin-left: 0.5rem;
}

.node-item {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

/* PC端节点项目优化 */
@media (min-width: 1025px) {
  .node-item {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border-radius: 10px;
  }

  .node-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }
}

.node-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

/* PC端节点头部优化 */
@media (min-width: 1025px) {
  .node-header {
    margin-bottom: 1rem;
  }
}

.node-type {
  background: #007bff;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* PC端节点类型标签优化 */
@media (min-width: 1025px) {
  .node-type {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
    border-radius: 6px;
  }
}

.node-details {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.5;
}

/* PC端节点详情优化 */
@media (min-width: 1025px) {
  .node-details {
    font-size: 1rem;
  }
}

.subscription-links {
  background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
  border-radius: 8px;
  padding: 1rem;
  margin-top: 2rem;
  border: 1px solid #dee2e6;
}

/* PC端订阅链接区域优化 */
@media (min-width: 1025px) {
  .subscription-links {
    padding: 2rem;
    border-radius: 12px;
    margin-top: 3rem;
  }
}

.link-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  padding: 0.75rem;
  background: white;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

/* PC端链接项目优化 */
@media (min-width: 1025px) {
  .link-item {
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    border-radius: 8px;
    font-size: 1.1rem;
  }

  .link-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.copy-btn {
  background: #28a745;
  width: auto;
  padding: 0.4rem 0.8rem;
  font-size: 0.85rem;
  font-weight: 500;
  border-radius: 4px;
  transition: all 0.3s ease;
}

/* PC端复制按钮优化 */
@media (min-width: 1025px) {
  .copy-btn {
    padding: 0.6rem 1.2rem;
    font-size: 0.95rem;
    border-radius: 6px;
  }
}

.copy-btn:hover {
  background: #218838;
  transform: translateY(-1px);
}

.alert {
  padding: 0.75rem;
  margin-bottom: 1rem;
  border-radius: 6px;
  font-weight: 500;
  border-left: 4px solid;
}

/* PC端警告框优化 */
@media (min-width: 1025px) {
  .alert {
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    border-radius: 8px;
    font-size: 1.05rem;
  }
}

.alert-success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
  border-left-color: #28a745;
}

.alert-error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-left-color: #dc3545;
}

/* 添加一些额外的PC端优化 */
@media (min-width: 1025px) {
  /* 优化删除按钮 */
  .btn-danger {
    padding: 0.6rem 1.2rem;
    font-size: 0.95rem;
    border-radius: 6px;
    transition: all 0.3s ease;
  }

  .btn-danger:hover {
    background: #c82333;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
  }

  /* 优化次要按钮 */
  .btn-secondary {
    margin-top: 1rem;
  }

  /* 优化文本区域 */
  textarea {
    min-height: 120px;
    resize: vertical;
  }

  /* 添加平滑滚动 */
  html {
    scroll-behavior: smooth;
  }

  /* 优化选择框 */
  select {
    cursor: pointer;
  }

  /* 添加加载状态样式 */
  button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
  }

  /* 登录页面特殊优化 */
  body:has(.container:only-child) {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* 登录容器在PC端的特殊样式 */
  body:has(.container:only-child) .container {
    max-width: 450px;
    margin: 0 auto;
  }
}

/* 为所有屏幕尺寸添加的通用改进 */
.container:only-child {
  /* 登录页面容器居中 */
  margin: auto;
}

/* 改进表单的视觉层次 */
.form-group:last-of-type {
  margin-bottom: 2rem;
}

/* 改进按钮组的间距 */
.btn-secondary + .btn-secondary,
.btn-danger + .btn-danger {
  margin-left: 0.5rem;
}

/* 响应式网格布局（为未来扩展准备） */
@media (min-width: 1200px) {
  .dashboard-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    max-width: 1600px;
  }

  /* 可以在未来添加侧边栏或多列布局 */
  .dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
  }
}
