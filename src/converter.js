/**
 * 修复后的节点转换器
 */

class NodeConverter {
  // 解析VMess链接
  parseVmess(config) {
    if (config.startsWith('vmess://')) {
      try {
        const base64Data = config.substring(8); // 移除 'vmess://' 前缀
        const jsonStr = atob(base64Data); // Base64解码
        const vmessConfig = JSON.parse(jsonStr);

        // 基础配置
        const node = {
          type: 'vmess',
          name: vmessConfig.ps || 'VMess节点',
          server: vmessConfig.add,
          port: parseInt(vmessConfig.port),
          uuid: vmessConfig.id,
          alterId: parseInt(vmessConfig.aid) || 0,
          cipher: vmessConfig.scy || 'auto',
          network: vmessConfig.net || 'tcp'
        };

        // 验证必要字段
        if (!node.server || !node.port || !node.uuid) {
          throw new Error('VMess链接缺少必要参数');
        }

        // TLS配置
        if (vmessConfig.tls === 'tls') {
          node.tls = true;
          node.sni = vmessConfig.sni || vmessConfig.host || node.server;
        }

        // 传输协议配置
        switch (node.network) {
          case 'tcp':
            node.headerType = vmessConfig.type || 'none';
            if (vmessConfig.host) {
              node.httpHeaders = { Host: vmessConfig.host };
            }
            break;
          case 'ws':
            node.path = vmessConfig.path || '/';
            node.host = vmessConfig.host || '';
            break;
          case 'h2':
            node.path = vmessConfig.path || '/';
            node.host = vmessConfig.host || '';
            break;
          case 'grpc':
            node.serviceName = vmessConfig.path || 'GunService';
            node.mode = vmessConfig.mode || 'gun';
            break;
        }

        return node;
      } catch (error) {
        throw new Error(`VMess链接解析失败: ${error.message}`);
      }
    }
    throw new Error('无效的VMess链接');
  }

  // 解析VLess链接
  parseVless(config) {
    if (config.startsWith('vless://')) {
      try {
        const url = new URL(config);
        const params = url.searchParams;
        
        // 基础配置
        const node = {
          type: 'vless',
          name: decodeURIComponent(url.hash.substring(1)) || 'VLess节点',
          server: url.hostname,
          port: parseInt(url.port),
          uuid: url.username,
          encryption: params.get('encryption') || 'none',
          network: params.get('type') || 'tcp'
        };

        // 验证必要字段
        if (!node.server || !node.port || !node.uuid) {
          throw new Error('VLess链接缺少必要参数');
        }

        // 安全传输配置
        const security = params.get('security');
        if (security === 'tls') {
          node.tls = true;
          node.sni = params.get('sni') || url.hostname;
          node.alpn = params.get('alpn');
          node.fingerprint = params.get('fp');
        } else if (security === 'reality') {
          node.reality = true;
          node.sni = params.get('sni') || url.hostname;
          node.fingerprint = params.get('fp') || 'chrome';
          node.publicKey = params.get('pbk');
          node.shortId = params.get('sid');
          node.spiderX = params.get('spx');
        }

        // Flow控制 (XTLS)
        const flow = params.get('flow');
        if (flow) {
          node.flow = flow;
        }

        // 传输协议配置
        switch (node.network) {
          case 'tcp':
            node.headerType = params.get('headerType') || 'none';
            break;
          case 'ws':
            node.path = params.get('path') || '/';
            node.host = params.get('host') || '';
            break;
          case 'grpc':
            node.serviceName = params.get('serviceName') || 'GunService';
            node.mode = params.get('mode') || 'gun';
            break;
          case 'h2':
            node.path = params.get('path') || '/';
            node.host = params.get('host') || '';
            break;
        }

        return node;
      } catch (error) {
        throw new Error(`VLess链接解析失败: ${error.message}`);
      }
    }
    throw new Error('无效的VLess链接');
  }

  // 解析节点
  parseNode(config, type) {
    switch (type) {
      case 'vmess':
        return this.parseVmess(config);
      case 'vless':
        return this.parseVless(config);
      default:
        throw new Error(`不支持的节点类型: ${type}`);
    }
  }

  // 节点转Clash代理配置
  nodeToClashProxy(node) {
    if (node.type === 'vmess') {
      const vmessProxy = {
        name: node.name,
        type: 'vmess',
        server: node.server,
        port: node.port,
        uuid: node.uuid,
        alterId: node.alterId || 0,
        cipher: node.cipher || 'auto',
        tfo: false,
        'skip-cert-verify': false
      };

      // TLS配置
      if (node.tls) {
        vmessProxy.tls = true;
        if (node.sni) {
          vmessProxy.servername = node.sni;
        }
      }

      // 网络配置
      vmessProxy.network = node.network || 'tcp';

      // 添加网络配置选项
      if (node.network === 'ws') {
        vmessProxy['ws-opts'] = {
          path: node.path || '/',
          headers: node.host ? { Host: node.host } : {}
        };
      } else if (node.network === 'h2') {
        vmessProxy['h2-opts'] = {
          host: [node.host || node.server],
          path: node.path || '/'
        };
      } else if (node.network === 'grpc') {
        vmessProxy['grpc-opts'] = {
          'grpc-service-name': node.serviceName || 'GunService'
        };
      } else if (node.network === 'tcp' && node.headerType === 'http') {
        vmessProxy['http-opts'] = {
          method: 'GET',
          path: ['/'],
          headers: node.httpHeaders || {}
        };
      }

      return vmessProxy;
    } else if (node.type === 'vless') {
      const vlessProxy = {
        name: node.name,
        type: 'vless',
        server: node.server,
        port: node.port,
        uuid: node.uuid,
        'client-fingerprint': 'chrome',
        tfo: false,
        'skip-cert-verify': false
      };

      // 设置TLS和安全传输
      if (node.reality) {
        vlessProxy.tls = true;
        // REALITY配置
        if (node.publicKey || node.shortId) {
          vlessProxy['reality-opts'] = {};
          if (node.publicKey) vlessProxy['reality-opts']['public-key'] = node.publicKey;
          if (node.shortId) vlessProxy['reality-opts']['short-id'] = node.shortId;
        }
      } else if (node.tls) {
        vlessProxy.tls = true;
      }

      // 添加服务器名称
      if (node.sni) {
        vlessProxy.servername = node.sni;
      }

      // 添加网络类型
      vlessProxy.network = node.network || 'tcp';

      // 添加Flow控制 (XTLS)
      if (node.flow) {
        vlessProxy.flow = node.flow;
      }

      // 添加客户端指纹
      if (node.fingerprint) {
        vlessProxy['client-fingerprint'] = node.fingerprint;
      }

      // 添加网络配置选项
      if (node.network === 'ws') {
        vlessProxy['ws-opts'] = {
          path: node.path || '/',
          headers: node.host ? { Host: node.host } : {}
        };
      } else if (node.network === 'grpc') {
        vlessProxy['grpc-opts'] = {
          'grpc-service-name': node.serviceName || 'GunService'
        };
      } else if (node.network === 'h2') {
        vlessProxy['h2-opts'] = {
          host: [node.host || node.server],
          path: node.path || '/'
        };
      }

      return vlessProxy;
    }
    return null;
  }

  // 转换为Clash配置
  toClashConfig(nodes, options = {}) {
    const proxies = nodes.map(node => this.nodeToClashProxy(node)).filter(Boolean);
    const proxyNames = proxies.map(p => p.name);

    return {
      // 全局配置
      'global-ua': 'clash',
      mode: 'rule',
      ipv6: true,
      'mixed-port': 7890,
      'allow-lan': true,
      'log-level': 'info',
      'external-controller': '0.0.0.0:9090',

      tun: {
        enable: true,
        stack: 'system',
        'dns-hijack': ['any:53'],
        'auto-route': true,
        'auto-detect-interface': true,
        device: "atri"
      },

      // 增强的DNS配置 - 防止DNS泄露
      dns: {
        enable: true,
        ipv6: true,
        'listen': '0.0.0.0:53',
        'enhanced-mode': 'fake-ip',
        'fake-ip-range': '********/8',
        nameserver: [
          '*********',
        ],
        'fake-ip-filter': [
          '*',
          '*.lan',
          '*.local',
          '*.localhost'
        ]
      },
      proxies: proxies,
      'proxy-groups': [
        {
          name: '节点选择',
          type: 'select',
          proxies: ['DIRECT'].concat(proxyNames),
          icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Proxy.png'
        },
        {
          name: '媒体服务',
          type: 'select',
          proxies: ['节点选择', 'DIRECT'].concat(proxyNames),
          icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Netflix.png'
        },
        {
          name: '微软服务',
          type: 'select',
          proxies: ['节点选择', 'DIRECT'].concat(proxyNames),
          icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Microsoft.png'
        },
        {
          name: '苹果服务',
          type: 'select',
          proxies: ['节点选择', 'DIRECT'].concat(proxyNames),
          icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Apple.png'
        },
        {
          name: 'CDN服务',
          type: 'select',
          proxies: ['节点选择', 'DIRECT'].concat(proxyNames),
          icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/OneDrive.png'
        },
        {
          name: 'AI服务',
          type: 'select',
          proxies: ['节点选择', 'DIRECT'].concat(proxyNames),
          icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/ChatGPT.png'
        },
        {
          name: 'Telegram',
          type: 'select',
          proxies: ['节点选择', 'DIRECT'].concat(proxyNames),
          icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Telegram.png'
        },
        {
          name: '爱奇艺&哔哩哔哩',
          type: 'select',
          proxies: ['DIRECT', '节点选择'].concat(proxyNames),
          icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/iQIYI&bilibili.png'
        },
        {
          name: 'Steam',
          type: 'select',
          proxies: ['DIRECT', '节点选择'].concat(proxyNames),
          icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Steam.png'
        },
        {
          name: 'Cloudflare',
          type: 'select',
          proxies: ['节点选择', 'DIRECT'].concat(proxyNames),
          icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Cloudflare.png'
        },
        {
          name: 'OneDrive',
          type: 'select',
          proxies: ['节点选择', 'DIRECT'].concat(proxyNames),
          icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/OneDrive.png'
        }
      ],
      rules: [
        // 拦截规则
        'RULE-SET,reject_non_ip,REJECT',
        'RULE-SET,reject_domainset,REJECT',
        'RULE-SET,reject_extra_domainset,REJECT',
        'RULE-SET,reject_non_ip_drop,REJECT-DROP',
        'RULE-SET,reject_non_ip_no_drop,REJECT',

        // 域名类规则
        'RULE-SET,telegram_non_ip,Telegram',
        'RULE-SET,apple_cdn,苹果服务',
        'RULE-SET,apple_cn_non_ip,苹果服务',
        'RULE-SET,microsoft_cdn_non_ip,微软服务',
        'RULE-SET,apple_services,苹果服务',
        'RULE-SET,microsoft_non_ip,微软服务',
        'RULE-SET,download_domainset,CDN服务',
        'RULE-SET,download_non_ip,CDN服务',
        'RULE-SET,cdn_domainset,CDN服务',
        'RULE-SET,cdn_non_ip,CDN服务',
        'RULE-SET,stream_non_ip,媒体服务',
        'RULE-SET,ai_non_ip,AI服务',
        'RULE-SET,global_non_ip,节点选择',
        'RULE-SET,domestic_non_ip,DIRECT',
        'RULE-SET,direct_non_ip,DIRECT',
        'RULE-SET,lan_non_ip,DIRECT',
        'GEOSITE,CN,DIRECT',

        // IP 类规则
        'RULE-SET,reject_ip,REJECT',
        'RULE-SET,telegram_ip,Telegram',
        'RULE-SET,stream_ip,媒体服务',
        'RULE-SET,lan_ip,DIRECT',
        'RULE-SET,domestic_ip,DIRECT',
        'RULE-SET,china_ip,DIRECT',
        'GEOIP,LAN,DIRECT',
        'GEOIP,CN,DIRECT',

        // 兜底规则
        'MATCH,节点选择'
      ],

      // 规则提供者
      'rule-providers': {
        reject_non_ip_no_drop: {
          type: 'http',
          behavior: 'classical',
          interval: 43200,
          format: 'text',
          proxy: '节点选择',
          url: 'https://ruleset.skk.moe/Clash/non_ip/reject-no-drop.txt',
          path: './rule_set/sukkaw_ruleset/reject_non_ip_no_drop.txt'
        },
        reject_non_ip_drop: {
          type: 'http',
          behavior: 'classical',
          interval: 43200,
          format: 'text',
          proxy: '节点选择',
          url: 'https://ruleset.skk.moe/Clash/non_ip/reject-drop.txt',
          path: './rule_set/sukkaw_ruleset/reject_non_ip_drop.txt'
        },
        reject_non_ip: {
          type: 'http',
          behavior: 'classical',
          interval: 43200,
          format: 'text',
          proxy: '节点选择',
          url: 'https://ruleset.skk.moe/Clash/non_ip/reject.txt',
          path: './rule_set/sukkaw_ruleset/reject_non_ip.txt'
        },
        reject_domainset: {
          type: 'http',
          behavior: 'domain',
          interval: 43200,
          format: 'text',
          proxy: '节点选择',
          url: 'https://ruleset.skk.moe/Clash/domainset/reject.txt',
          path: './rule_set/sukkaw_ruleset/reject_domainset.txt'
        },
        reject_extra_domainset: {
          type: 'http',
          behavior: 'domain',
          interval: 43200,
          format: 'text',
          proxy: '节点选择',
          url: 'https://ruleset.skk.moe/Clash/domainset/reject_extra.txt',
          path: './sukkaw_ruleset/reject_domainset_extra.txt'
        },
        reject_ip: {
          type: 'http',
          behavior: 'classical',
          interval: 43200,
          format: 'text',
          proxy: '节点选择',
          url: 'https://ruleset.skk.moe/Clash/ip/reject.txt',
          path: './rule_set/sukkaw_ruleset/reject_ip.txt'
        },
        cdn_domainset: {
          type: 'http',
          behavior: 'domain',
          interval: 43200,
          format: 'text',
          proxy: '节点选择',
          url: 'https://ruleset.skk.moe/Clash/domainset/cdn.txt',
          path: './rule_set/sukkaw_ruleset/cdn_domainset.txt'
        },
        cdn_non_ip: {
          type: 'http',
          behavior: 'domain',
          interval: 43200,
          format: 'text',
          proxy: '节点选择',
          url: 'https://ruleset.skk.moe/Clash/non_ip/cdn.txt',
          path: './rule_set/sukkaw_ruleset/cdn_non_ip.txt'
        },
        stream_non_ip: {
          type: 'http',
          behavior: 'classical',
          interval: 43200,
          format: 'text',
          proxy: '节点选择',
          url: 'https://ruleset.skk.moe/Clash/non_ip/stream.txt',
          path: './rule_set/sukkaw_ruleset/stream_non_ip.txt'
        },
        stream_ip: {
          type: 'http',
          behavior: 'classical',
          interval: 43200,
          format: 'text',
          proxy: '节点选择',
          url: 'https://ruleset.skk.moe/Clash/ip/stream.txt',
          path: './rule_set/sukkaw_ruleset/stream_ip.txt'
        },
        ai_non_ip: {
          type: 'http',
          behavior: 'classical',
          interval: 43200,
          format: 'text',
          proxy: '节点选择',
          url: 'https://ruleset.skk.moe/Clash/non_ip/ai.txt',
          path: './rule_set/sukkaw_ruleset/ai_non_ip.txt'
        },
        telegram_non_ip: {
          type: 'http',
          behavior: 'classical',
          interval: 43200,
          format: 'text',
          proxy: '节点选择',
          url: 'https://ruleset.skk.moe/Clash/non_ip/telegram.txt',
          path: './rule_set/sukkaw_ruleset/telegram_non_ip.txt'
        },
        telegram_ip: {
          type: 'http',
          behavior: 'classical',
          interval: 43200,
          format: 'text',
          proxy: '节点选择',
          url: 'https://ruleset.skk.moe/Clash/ip/telegram.txt',
          path: './rule_set/sukkaw_ruleset/telegram_ip.txt'
        },
        apple_cdn: {
          type: 'http',
          behavior: 'domain',
          interval: 43200,
          format: 'text',
          proxy: '节点选择',
          url: 'https://ruleset.skk.moe/Clash/domainset/apple_cdn.txt',
          path: './rule_set/sukkaw_ruleset/apple_cdn.txt'
        },
        apple_services: {
          type: 'http',
          behavior: 'classical',
          interval: 43200,
          format: 'text',
          proxy: '节点选择',
          url: 'https://ruleset.skk.moe/Clash/non_ip/apple_services.txt',
          path: './rule_set/sukkaw_ruleset/apple_services.txt'
        },
        apple_cn_non_ip: {
          type: 'http',
          behavior: 'classical',
          interval: 43200,
          format: 'text',
          proxy: '节点选择',
          url: 'https://ruleset.skk.moe/Clash/non_ip/apple_cn.txt',
          path: './rule_set/sukkaw_ruleset/apple_cn_non_ip.txt'
        },
        microsoft_cdn_non_ip: {
          type: 'http',
          behavior: 'classical',
          interval: 43200,
          format: 'text',
          proxy: '节点选择',
          url: 'https://ruleset.skk.moe/Clash/non_ip/microsoft_cdn.txt',
          path: './rule_set/sukkaw_ruleset/microsoft_cdn_non_ip.txt'
        },
        microsoft_non_ip: {
          type: 'http',
          behavior: 'classical',
          interval: 43200,
          format: 'text',
          proxy: '节点选择',
          url: 'https://ruleset.skk.moe/Clash/non_ip/microsoft.txt',
          path: './rule_set/sukkaw_ruleset/microsoft_non_ip.txt'
        },
        download_domainset: {
          type: 'http',
          behavior: 'domain',
          interval: 43200,
          format: 'text',
          proxy: '节点选择',
          url: 'https://ruleset.skk.moe/Clash/domainset/download.txt',
          path: './rule_set/sukkaw_ruleset/download_domainset.txt'
        },
        download_non_ip: {
          type: 'http',
          behavior: 'domain',
          interval: 43200,
          format: 'text',
          proxy: '节点选择',
          url: 'https://ruleset.skk.moe/Clash/non_ip/download.txt',
          path: './rule_set/sukkaw_ruleset/download_non_ip.txt'
        },
        lan_non_ip: {
          type: 'http',
          behavior: 'classical',
          interval: 43200,
          format: 'text',
          proxy: '节点选择',
          url: 'https://ruleset.skk.moe/Clash/non_ip/lan.txt',
          path: './rule_set/sukkaw_ruleset/lan_non_ip.txt'
        },
        lan_ip: {
          type: 'http',
          behavior: 'classical',
          interval: 43200,
          format: 'text',
          proxy: '节点选择',
          url: 'https://ruleset.skk.moe/Clash/ip/lan.txt',
          path: './rule_set/sukkaw_ruleset/lan_ip.txt'
        },
        domestic_non_ip: {
          type: 'http',
          behavior: 'classical',
          interval: 43200,
          format: 'text',
          proxy: '节点选择',
          url: 'https://ruleset.skk.moe/Clash/non_ip/domestic.txt',
          path: './rule_set/sukkaw_ruleset/domestic_non_ip.txt'
        },
        direct_non_ip: {
          type: 'http',
          behavior: 'classical',
          interval: 43200,
          format: 'text',
          proxy: '节点选择',
          url: 'https://ruleset.skk.moe/Clash/non_ip/direct.txt',
          path: './rule_set/sukkaw_ruleset/direct_non_ip.txt'
        },
        global_non_ip: {
          type: 'http',
          behavior: 'classical',
          interval: 43200,
          format: 'text',
          proxy: '节点选择',
          url: 'https://ruleset.skk.moe/Clash/non_ip/global.txt',
          path: './rule_set/sukkaw_ruleset/global_non_ip.txt'
        },
        domestic_ip: {
          type: 'http',
          behavior: 'classical',
          interval: 43200,
          format: 'text',
          proxy: '节点选择',
          url: 'https://ruleset.skk.moe/Clash/ip/domestic.txt',
          path: './rule_set/sukkaw_ruleset/domestic_ip.txt'
        },
        china_ip: {
          type: 'http',
          behavior: 'ipcidr',
          interval: 43200,
          format: 'text',
          proxy: '节点选择',
          url: 'https://ruleset.skk.moe/Clash/ip/china_ip.txt',
          path: './rule_set/sukkaw_ruleset/china_ip.txt'
        }
      }
    };
  }

  // 转换为V2Ray订阅
  toV2raySubscription(nodes) {
    const configs = nodes.map(node => {
      if (node.type === 'vmess') {
        return this.nodeToVmessLink(node);
      } else if (node.type === 'vless') {
        return this.nodeToVlessLink(node);
      }
      return null;
    }).filter(Boolean);

    return btoa(configs.join('\n'));
  }

  // 节点转VMess链接
  nodeToVmessLink(node) {
    const vmessConfig = {
      v: '2',
      ps: node.name,
      add: node.server,
      port: node.port,
      id: node.uuid,
      aid: node.alterId || 0,
      scy: node.cipher || 'auto',
      net: node.network || 'tcp',
      type: node.headerType || 'none',
      host: node.host || '',
      path: node.path || '',
      tls: node.tls ? 'tls' : 'none'
    };

    // 添加 SNI 配置
    if (node.sni) {
      vmessConfig.sni = node.sni;
    }

    const base64Config = btoa(JSON.stringify(vmessConfig));
    return `vmess://${base64Config}`;
  }

  // 节点转VLess链接
  nodeToVlessLink(node) {
    const params = new URLSearchParams();
    params.set('encryption', node.encryption || 'none');
    params.set('type', node.network || 'tcp');
    
    // 安全传输配置
    if (node.reality) {
      params.set('security', 'reality');
      if (node.sni) params.set('sni', node.sni);
      if (node.fingerprint) params.set('fp', node.fingerprint);
      if (node.publicKey) params.set('pbk', node.publicKey);
      if (node.shortId) params.set('sid', node.shortId);
    } else if (node.tls) {
      params.set('security', 'tls');
      if (node.sni) params.set('sni', node.sni);
      if (node.alpn) params.set('alpn', node.alpn);
      if (node.fingerprint) params.set('fp', node.fingerprint);
    }
    
    // Flow控制
    if (node.flow) params.set('flow', node.flow);
    
    // 传输协议配置
    switch (node.network) {
      case 'ws':
        if (node.path) params.set('path', node.path);
        if (node.host) params.set('host', node.host);
        break;
      case 'grpc':
        if (node.serviceName) params.set('serviceName', node.serviceName);
        if (node.mode) params.set('mode', node.mode);
        break;
      case 'h2':
        if (node.path) params.set('path', node.path);
        if (node.host) params.set('host', node.host);
        break;
    }
    
    return `vless://${node.uuid}@${node.server}:${node.port}?${params.toString()}#${encodeURIComponent(node.name)}`;
  }
}

module.exports = { NodeConverter };
